// Database Store Search and Filter Functionality

// Search configuration
const searchConfig = {
    minSearchLength: 2,
    searchDelay: 300,
    maxSuggestions: 8,
    highlightClass: 'search-highlight'
};

// Search state
let searchState = {
    currentQuery: '',
    suggestions: [],
    isSearching: false,
    searchHistory: []
};

// Initialize search functionality
function initializeSearchSystem() {
    setupSearchInput();
    // setupSearchSuggestions(); // This function is handled in setupSearchInput
    setupAdvancedFilters();
    loadSearchHistory();

    console.log('Search system initialized');
}

// Setup search input
function setupSearchInput() {
    const searchInput = document.getElementById('searchInput');
    if (!searchInput) return;

    // Create search suggestions container
    createSearchSuggestions(searchInput);

    // Add event listeners
    searchInput.addEventListener('input', handleSearchInput);
    searchInput.addEventListener('focus', showSearchSuggestions);
    searchInput.addEventListener('blur', hideSearchSuggestions);
    searchInput.addEventListener('keydown', handleSearchKeydown);

    // Add search icon click handler
    const searchIcon = searchInput.parentElement.querySelector('.fas.fa-search');
    if (searchIcon) {
        searchIcon.addEventListener('click', performSearch);
    }
}

// Create search suggestions dropdown
function createSearchSuggestions(searchInput) {
    const suggestionsContainer = document.createElement('div');
    suggestionsContainer.className = 'search-suggestions';
    suggestionsContainer.id = 'searchSuggestions';
    
    suggestionsContainer.innerHTML = `
        <div class="suggestions-header">
            <span>اقتراحات البحث</span>
        </div>
        <div class="suggestions-list" id="suggestionsList"></div>
        <div class="suggestions-footer">
            <button class="btn-clear-history" onclick="clearSearchHistory()">
                <i class="fas fa-trash me-2"></i>
                مسح السجل
            </button>
        </div>
    `;
    
    // Add CSS for suggestions
    if (!document.getElementById('searchSuggestionsCSS')) {
        const style = document.createElement('style');
        style.id = 'searchSuggestionsCSS';
        style.textContent = `
            .search-suggestions {
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: white;
                border: 1px solid var(--gray-200);
                border-radius: var(--border-radius);
                box-shadow: var(--shadow-lg);
                z-index: 1000;
                max-height: 300px;
                overflow-y: auto;
                display: none;
            }
            
            .suggestions-header {
                padding: 0.75rem 1rem;
                background: var(--gray-100);
                border-bottom: 1px solid var(--gray-200);
                font-weight: 600;
                font-size: 0.9rem;
                color: var(--gray-700);
            }
            
            .suggestions-list {
                max-height: 200px;
                overflow-y: auto;
            }
            
            .suggestion-item {
                padding: 0.75rem 1rem;
                cursor: pointer;
                border-bottom: 1px solid var(--gray-100);
                transition: background-color 0.2s ease;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }
            
            .suggestion-item:hover,
            .suggestion-item.active {
                background: var(--gray-100);
            }
            
            .suggestion-item i {
                color: var(--gray-400);
                width: 16px;
            }
            
            .suggestion-text {
                flex: 1;
            }
            
            .suggestion-count {
                font-size: 0.8rem;
                color: var(--gray-500);
            }
            
            .suggestions-footer {
                padding: 0.5rem 1rem;
                border-top: 1px solid var(--gray-200);
                background: var(--gray-50);
            }
            
            .btn-clear-history {
                background: none;
                border: none;
                color: var(--gray-600);
                font-size: 0.8rem;
                cursor: pointer;
                padding: 0.25rem 0.5rem;
                border-radius: var(--border-radius);
                transition: all 0.2s ease;
            }
            
            .btn-clear-history:hover {
                background: var(--gray-200);
                color: var(--gray-800);
            }
            
            .search-highlight {
                background: var(--accent-color);
                color: white;
                padding: 0.1rem 0.2rem;
                border-radius: 3px;
                font-weight: 600;
            }
        `;
        document.head.appendChild(style);
    }
    
    searchInput.parentElement.style.position = 'relative';
    searchInput.parentElement.appendChild(suggestionsContainer);
}

// Handle search input
function handleSearchInput(event) {
    const query = event.target.value.trim();
    searchState.currentQuery = query;

    if (query.length >= searchConfig.minSearchLength) {
        searchState.isSearching = true;
        
        // Debounce search
        clearTimeout(searchState.searchTimeout);
        searchState.searchTimeout = setTimeout(() => {
            generateSearchSuggestions(query);
            showSearchSuggestions();
        }, searchConfig.searchDelay);
    } else {
        hideSearchSuggestions();
        searchState.isSearching = false;
    }
}

// Generate search suggestions
function generateSearchSuggestions(query) {
    const suggestions = [];
    const queryLower = query.toLowerCase();

    // Get categories data
    const categoriesData = window.DatabaseDataLoader ?
        window.DatabaseDataLoader.getAllCategories() :
        {};

    // Search in categories
    Object.entries(categoriesData).forEach(([key, category]) => {
        if (category.name.toLowerCase().includes(queryLower)) {
            suggestions.push({
                type: 'category',
                text: category.name,
                icon: 'fas fa-folder',
                action: () => window.DatabaseCategories ? window.DatabaseCategories.filterByCategory(key) : null,
                count: category.specializations.length
            });
        }

        // Search in specializations
        category.specializations.forEach(spec => {
            if (spec.name.toLowerCase().includes(queryLower)) {
                suggestions.push({
                    type: 'specialization',
                    text: spec.name,
                    icon: 'fas fa-lightbulb',
                    action: () => searchForSpecialization(spec.name),
                    count: spec.count
                });
            }
        });
    });

    // Add search history
    searchState.searchHistory.forEach(historyItem => {
        if (historyItem.toLowerCase().includes(queryLower) && 
            !suggestions.find(s => s.text.toLowerCase() === historyItem.toLowerCase())) {
            suggestions.push({
                type: 'history',
                text: historyItem,
                icon: 'fas fa-history',
                action: () => performSearchWithQuery(historyItem)
            });
        }
    });

    // Limit suggestions
    searchState.suggestions = suggestions.slice(0, searchConfig.maxSuggestions);
    renderSearchSuggestions();
}

// Render search suggestions
function renderSearchSuggestions() {
    const suggestionsList = document.getElementById('suggestionsList');
    if (!suggestionsList) return;

    if (searchState.suggestions.length === 0) {
        suggestionsList.innerHTML = `
            <div class="suggestion-item">
                <i class="fas fa-search"></i>
                <span class="suggestion-text">لا توجد اقتراحات</span>
            </div>
        `;
        return;
    }

    suggestionsList.innerHTML = searchState.suggestions.map((suggestion, index) => {
        const highlightedText = highlightSearchTerm(suggestion.text, searchState.currentQuery);
        const countText = suggestion.count ? `(${window.DatabaseStore ? window.DatabaseStore.formatNumber(suggestion.count) : suggestion.count})` : '';
        
        return `
            <div class="suggestion-item" data-index="${index}" onclick="selectSuggestion(${index})">
                <i class="${suggestion.icon}"></i>
                <span class="suggestion-text">${highlightedText}</span>
                <span class="suggestion-count">${countText}</span>
            </div>
        `;
    }).join('');
}

// Highlight search term in text
function highlightSearchTerm(text, term) {
    if (!term) return text;
    
    const regex = new RegExp(`(${term})`, 'gi');
    return text.replace(regex, `<span class="${searchConfig.highlightClass}">$1</span>`);
}

// Show search suggestions
function showSearchSuggestions() {
    const suggestions = document.getElementById('searchSuggestions');
    if (suggestions && searchState.suggestions.length > 0) {
        suggestions.style.display = 'block';
    }
}

// Hide search suggestions
function hideSearchSuggestions() {
    setTimeout(() => {
        const suggestions = document.getElementById('searchSuggestions');
        if (suggestions) {
            suggestions.style.display = 'none';
        }
    }, 200);
}

// Handle search keydown events
function handleSearchKeydown(event) {
    const suggestions = document.getElementById('searchSuggestions');
    if (!suggestions || suggestions.style.display === 'none') return;

    const suggestionItems = suggestions.querySelectorAll('.suggestion-item');
    let activeIndex = Array.from(suggestionItems).findIndex(item => item.classList.contains('active'));

    switch (event.key) {
        case 'ArrowDown':
            event.preventDefault();
            activeIndex = (activeIndex + 1) % suggestionItems.length;
            updateActiveSuggestion(suggestionItems, activeIndex);
            break;
            
        case 'ArrowUp':
            event.preventDefault();
            activeIndex = activeIndex <= 0 ? suggestionItems.length - 1 : activeIndex - 1;
            updateActiveSuggestion(suggestionItems, activeIndex);
            break;
            
        case 'Enter':
            event.preventDefault();
            if (activeIndex >= 0) {
                selectSuggestion(activeIndex);
            } else {
                performSearch();
            }
            break;
            
        case 'Escape':
            hideSearchSuggestions();
            break;
    }
}

// Update active suggestion
function updateActiveSuggestion(suggestionItems, activeIndex) {
    suggestionItems.forEach((item, index) => {
        item.classList.toggle('active', index === activeIndex);
    });
}

// Select suggestion
function selectSuggestion(index) {
    const suggestion = searchState.suggestions[index];
    if (suggestion) {
        addToSearchHistory(suggestion.text);
        suggestion.action();
        hideSearchSuggestions();
    }
}

// Perform search
function performSearch() {
    const searchInput = document.getElementById('searchInput');
    if (!searchInput) return;

    const query = searchInput.value.trim();
    if (query) {
        performSearchWithQuery(query);
    }
}

// Perform search with specific query
function performSearchWithQuery(query) {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.value = query;
    }

    // Update global filters
    if (window.currentFilters) {
        window.currentFilters.search = query;
    }
    if (window.currentPage !== undefined) {
        window.currentPage = 1;
    }
    
    addToSearchHistory(query);

    // Call loadProducts from DatabaseCategories
    if (window.DatabaseCategories && window.DatabaseCategories.loadProducts) {
        window.DatabaseCategories.loadProducts();
    }

    // Scroll to products section
    if (window.DatabaseStore && window.DatabaseStore.scrollToSection) {
        window.DatabaseStore.scrollToSection('products');
    }
    hideSearchSuggestions();
    
    // Show notification
    if (window.DatabaseStore && window.DatabaseStore.showNotification) {
        window.DatabaseStore.showNotification('البحث', `البحث عن: ${query}`, 'info');
    }
}

// Search for specific specialization
function searchForSpecialization(specialization) {
    performSearchWithQuery(specialization);
}

// Add to search history
function addToSearchHistory(query) {
    if (!query || searchState.searchHistory.includes(query)) return;
    
    searchState.searchHistory.unshift(query);
    searchState.searchHistory = searchState.searchHistory.slice(0, 10); // Keep only last 10 searches
    
    saveSearchHistory();
}

// Save search history to localStorage
function saveSearchHistory() {
    try {
        localStorage.setItem('databaseStoreSearchHistory', JSON.stringify(searchState.searchHistory));
    } catch (error) {
        console.error('Error saving search history:', error);
    }
}

// Load search history from localStorage
function loadSearchHistory() {
    try {
        const savedHistory = localStorage.getItem('databaseStoreSearchHistory');
        if (savedHistory) {
            searchState.searchHistory = JSON.parse(savedHistory);
        }
    } catch (error) {
        console.error('Error loading search history:', error);
        searchState.searchHistory = [];
    }
}

// Clear search history
function clearSearchHistory() {
    searchState.searchHistory = [];
    saveSearchHistory();
    hideSearchSuggestions();
    // Show notification
    if (window.DatabaseStore && window.DatabaseStore.showNotification) {
        window.DatabaseStore.showNotification('تم المسح', 'تم مسح سجل البحث', 'success');
    }
}

// Setup advanced filters
function setupAdvancedFilters() {
    // Price range filter
    setupPriceRangeFilter();
    
    // Sort options
    setupSortOptions();
    
    // View options
    setupViewOptions();
}

// Setup price range filter
function setupPriceRangeFilter() {
    const priceFilter = document.getElementById('priceFilter');
    if (!priceFilter) return;

    // Add custom price range option
    const customOption = document.createElement('option');
    customOption.value = 'custom';
    customOption.textContent = 'نطاق مخصص';
    priceFilter.appendChild(customOption);

    priceFilter.addEventListener('change', function() {
        if (this.value === 'custom') {
            showCustomPriceRange();
        }
    });
}

// Show custom price range modal
function showCustomPriceRange() {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تحديد نطاق السعر</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">السعر الأدنى</label>
                            <input type="number" class="form-control" id="minPrice" min="0" placeholder="0">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">السعر الأعلى</label>
                            <input type="number" class="form-control" id="maxPrice" min="0" placeholder="1000">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="applyCustomPriceRange()">تطبيق</button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
    
    modal.addEventListener('hidden.bs.modal', () => {
        modal.remove();
    });
}

// Apply custom price range
function applyCustomPriceRange() {
    const minPrice = document.getElementById('minPrice').value;
    const maxPrice = document.getElementById('maxPrice').value;
    
    if (minPrice || maxPrice) {
        const min = minPrice || 0;
        const max = maxPrice || '+';
        // Update global filters
        if (window.currentFilters) {
            window.currentFilters.priceRange = `${min}-${max}`;
        }
        if (window.currentPage !== undefined) {
            window.currentPage = 1;
        }

        // Call loadProducts from DatabaseCategories
        if (window.DatabaseCategories && window.DatabaseCategories.loadProducts) {
            window.DatabaseCategories.loadProducts();
        }

        // Show notification
        const formatPrice = window.DatabaseStore ? window.DatabaseStore.formatPrice : (price) => `${price} ريال`;
        const showNotification = window.DatabaseStore ? window.DatabaseStore.showNotification : () => {};
        showNotification('تم التطبيق', `نطاق السعر: ${formatPrice(min)} - ${max === '+' ? 'ما فوق' : formatPrice(max)}`, 'success');
    }
    
    // Close modal
    const modal = bootstrap.Modal.getInstance(document.querySelector('.modal'));
    if (modal) modal.hide();
}

// Setup sort options
function setupSortOptions() {
    // Add sort dropdown if not exists
    const searchSection = document.querySelector('.search-section .row');
    if (searchSection && !document.getElementById('sortSelect')) {
        const sortCol = document.createElement('div');
        sortCol.className = 'col-lg-2';
        sortCol.innerHTML = `
            <select class="form-select" id="sortSelect">
                <option value="relevance">الأكثر صلة</option>
                <option value="price-low">السعر: من الأقل للأعلى</option>
                <option value="price-high">السعر: من الأعلى للأقل</option>
                <option value="count-high">الأكثر بيانات</option>
                <option value="name">الاسم أبجدياً</option>
            </select>
        `;
        
        // Insert before the last column (search button)
        const lastCol = searchSection.lastElementChild;
        searchSection.insertBefore(sortCol, lastCol);
        
        // Add event listener
        document.getElementById('sortSelect').addEventListener('change', handleSortChange);
    }
}

// Handle sort change
function handleSortChange(event) {
    const sortValue = event.target.value;

    // Update global filters
    if (window.currentFilters) {
        window.currentFilters.sort = sortValue;
    }
    if (window.currentPage !== undefined) {
        window.currentPage = 1;
    }

    // Call loadProducts from DatabaseCategories
    if (window.DatabaseCategories && window.DatabaseCategories.loadProducts) {
        window.DatabaseCategories.loadProducts();
    }
}

// Setup view options
function setupViewOptions() {
    // Add view toggle buttons
    const productsSection = document.querySelector('.products-section .section-header');
    if (productsSection && !document.getElementById('viewToggle')) {
        const viewToggle = document.createElement('div');
        viewToggle.id = 'viewToggle';
        viewToggle.className = 'view-toggle mt-3';
        viewToggle.innerHTML = `
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary active" data-view="grid">
                    <i class="fas fa-th"></i>
                </button>
                <button type="button" class="btn btn-outline-primary" data-view="list">
                    <i class="fas fa-list"></i>
                </button>
            </div>
        `;
        
        productsSection.appendChild(viewToggle);
        
        // Add event listeners
        viewToggle.querySelectorAll('button').forEach(btn => {
            btn.addEventListener('click', handleViewChange);
        });
    }
}

// Handle view change
function handleViewChange(event) {
    const viewType = event.target.closest('button').dataset.view;
    const buttons = event.target.closest('.btn-group').querySelectorAll('button');
    
    buttons.forEach(btn => btn.classList.remove('active'));
    event.target.closest('button').classList.add('active');
    
    const productsGrid = document.getElementById('productsGrid');
    if (productsGrid) {
        productsGrid.className = viewType === 'list' ? 'products-list' : 'products-grid';
    }
}

// Initialize search system when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeSearchSystem();
});

// Export search functions
window.DatabaseSearch = {
    performSearch,
    performSearchWithQuery,
    searchForSpecialization,
    clearSearchHistory,
    applyCustomPriceRange,
    searchState
};
