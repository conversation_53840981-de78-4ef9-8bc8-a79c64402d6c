// Database Data Loader - Load categories from JSON files

// Global variables for data
let categoriesData = {};
let productsData = [];
let isDataLoaded = false;

// Load data from embedded JSON data
async function loadDatabaseData() {
    try {
        console.log('Loading database categories from embedded data...');

        // Use real database data
        const realData = window.getRealDatabaseData ? window.getRealDatabaseData() : {};
        const additionalData = window.getAdditionalRealData ? window.getAdditionalRealData() : {};

        // Merge real data with additional data
        categoriesData = { ...realData, ...additionalData };

        // Generate products data
        generateProductsFromJSON();

        isDataLoaded = true;
        console.log('Database data loaded successfully');
        console.log('Categories loaded:', Object.keys(categoriesData).length);
        console.log('Products generated:', productsData.length);

        // Trigger data loaded event
        document.dispatchEvent(new CustomEvent('databaseDataLoaded'));

        return true;
    } catch (error) {
        console.error('Error loading database data:', error);

        // Fallback to hardcoded data if loading fails
        console.log('Falling back to hardcoded data...');
        loadFallbackData();
        return false;
    }
}

// Generate products from JSON data
function generateProductsFromJSON() {
    productsData = [];
    
    Object.entries(categoriesData).forEach(([categoryKey, category]) => {
        category.specializations.forEach(spec => {
            const product = {
                id: spec.id || `${categoryKey}-${spec.name.replace(/\s+/g, '-')}`,
                title: spec.title || `قاعدة بيانات ${spec.name}`,
                category: category.name,
                categoryKey: categoryKey,
                description: spec.description || `قاعدة بيانات شاملة ومحدثة لـ ${spec.name}`,
                count: spec.count,
                price: spec.price,
                originalPrice: Math.round(spec.price * 1.3),
                discount: 23,
                features: [
                    'دليل تنفيذ مفصل وخطة عمل واضحة',
                    'دراسة جدوى شاملة مع تحليل السوق',
                    'استراتيجيات التسويق والربح المجربة',
                    'دعم فني متخصص على مدار الساعة',
                    'ضمان النجاح أو استرداد المال خلال 30 يوم',
                    'أفكار مجربة ومختبرة بمعدل نجاح 95%'
                ],
                rating: (4.2 + Math.random() * 0.8).toFixed(1),
                reviews: Math.floor(Math.random() * 100) + 20,
                specialization: spec.name,
                locations: spec.locations || [],
                specialties: spec.specialties || [],
                types: spec.types || [],
                categories: spec.categories || [],
                services: spec.services || [],
                sectors: spec.sectors || []
            };
            productsData.push(product);
        });
    });
}

// Fallback data in case JSON loading fails
function loadFallbackData() {
    categoriesData = {
        "mobile-apps": {
            name: 'تطبيقات الجوال',
            icon: 'fas fa-mobile-alt',
            description: 'أفكار مبتكرة لتطبيقات الهواتف الذكية',
            specializations: [
                {
                    id: 'delivery-apps',
                    name: 'تطبيقات التوصيل والخدمات',
                    count: 15,
                    price: 299,
                    title: 'مجموعة شاملة من أفكار تطبيقات التوصيل والخدمات المنزلية',
                    description: '15 فكرة مبتكرة لتطبيقات التوصيل والخدمات المنزلية مع دراسة جدوى مفصلة وخطة تنفيذ واضحة'
                },
                {
                    id: 'social-apps',
                    name: 'تطبيقات التواصل الاجتماعي',
                    count: 12,
                    price: 399,
                    title: 'أفكار مبتكرة لتطبيقات التواصل الاجتماعي والمجتمعات الرقمية',
                    description: '12 فكرة مبدعة لتطبيقات التواصل الاجتماعي مع استراتيجيات الربح والنمو'
                }
            ]
        },
        "profit-projects": {
            name: 'مشاريع الربح',
            icon: 'fas fa-chart-line',
            description: 'مشاريع مربحة ومجربة للاستثمار',
            specializations: [
                {
                    id: 'dropshipping-business',
                    name: 'مشاريع الدروب شيبنغ',
                    count: 30,
                    price: 199,
                    title: 'دليل شامل لمشاريع الدروب شيبنغ المربحة',
                    description: '30 فكرة مشروع دروب شيبنغ مع قوائم الموردين واستراتيجيات التسويق'
                }
            ]
        }
    };
    
    generateProductsFromJSON();
    isDataLoaded = true;
    document.dispatchEvent(new CustomEvent('databaseDataLoaded'));
}

// Get category data by key
function getCategoryData(categoryKey) {
    return categoriesData[categoryKey] || null;
}

// Get all categories
function getAllCategories() {
    return categoriesData;
}

// Get products by category
function getProductsByCategory(categoryKey) {
    return productsData.filter(product => product.categoryKey === categoryKey);
}

// Get product by ID
function getProductById(productId) {
    return productsData.find(product => product.id === productId);
}

// Search products
function searchProducts(query) {
    if (!query) return productsData;
    
    const searchTerm = query.toLowerCase();
    return productsData.filter(product => 
        product.title.toLowerCase().includes(searchTerm) ||
        product.description.toLowerCase().includes(searchTerm) ||
        product.category.toLowerCase().includes(searchTerm) ||
        product.specialization.toLowerCase().includes(searchTerm)
    );
}

// Filter products
function filterProducts(filters = {}) {
    let filtered = [...productsData];
    
    // Category filter
    if (filters.category) {
        filtered = filtered.filter(product => product.categoryKey === filters.category);
    }
    
    // Price range filter
    if (filters.priceRange) {
        const [min, max] = filters.priceRange.split('-').map(p => p === '+' ? Infinity : parseInt(p));
        filtered = filtered.filter(product => 
            product.price >= min && (max === Infinity || product.price <= max)
        );
    }
    
    // Search filter
    if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        filtered = filtered.filter(product => 
            product.title.toLowerCase().includes(searchTerm) ||
            product.description.toLowerCase().includes(searchTerm) ||
            product.category.toLowerCase().includes(searchTerm)
        );
    }
    
    // Sort filter
    if (filters.sort) {
        switch (filters.sort) {
            case 'price-low':
                filtered.sort((a, b) => a.price - b.price);
                break;
            case 'price-high':
                filtered.sort((a, b) => b.price - a.price);
                break;
            case 'count-high':
                filtered.sort((a, b) => b.count - a.count);
                break;
            case 'name':
                filtered.sort((a, b) => a.title.localeCompare(b.title, 'ar'));
                break;
            default:
                // relevance - keep original order
                break;
        }
    }
    
    return filtered;
}

// Get statistics
function getDatabaseStatistics() {
    const totalProducts = productsData.length;
    const totalContacts = productsData.reduce((sum, product) => sum + product.count, 0);
    const totalCategories = Object.keys(categoriesData).length;
    const averagePrice = Math.round(productsData.reduce((sum, product) => sum + product.price, 0) / totalProducts);
    
    return {
        totalProducts,
        totalContacts,
        totalCategories,
        averagePrice,
        priceRange: {
            min: Math.min(...productsData.map(p => p.price)),
            max: Math.max(...productsData.map(p => p.price))
        }
    };
}

// Get embedded categories data - Returns app ideas data
function getEmbeddedCategoriesData() {
    // Return the same data as getRealDatabaseData for consistency
    return window.getRealDatabaseData ? window.getRealDatabaseData() : {};
}

// Initialize data loading when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, starting data loading...');
    loadDatabaseData();
});

// Export functions for global use
window.DatabaseDataLoader = {
    loadDatabaseData,
    getCategoryData,
    getAllCategories,
    getProductsByCategory,
    getProductById,
    searchProducts,
    filterProducts,
    getDatabaseStatistics,
    isDataLoaded: () => isDataLoaded,
    categoriesData: () => categoriesData,
    productsData: () => productsData
};
